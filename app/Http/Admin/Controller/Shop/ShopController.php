<?php

namespace App\Http\Admin\Controller\Shop;

use App\Service\Shop\ShopService as Service;
use App\Http\Admin\Request\Shop\ShopRequest as Request;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;

//use App\Annotation\ApiName;
//use App\Http\Common\Middleware\OperationHyperfRouterAnnotationMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Hyperf\Swagger\Annotation as OA;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;

//use Hyperf\HttpServer\Annotation\Controller;
//use Hyperf\HttpServer\Annotation\RequestMapping;

#[OA\Tag('{店铺列表}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
//#[Middleware(middleware: OperationHyperfRouterAnnotationMiddleware::class, priority: 98)]
final class ShopController extends AbstractController
{
    public function __construct(
        private readonly Service     $service,
        private readonly CurrentUser $currentUser
    )
    {
    }

    #[Get(
        path: '/admin/shop/shop/list',
        operationId: 'shop:shop:list',
        summary: '店铺列表列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['店铺列表'],
    )]
//    #[ApiName(name: '店铺列表列表')]
//    #[RequestMapping(path: 'shop/shop/list', methods:"get")]
    #[Permission(code: 'shop:shop:list')]
    public function pageList(): Result
    {
        return $this->success(
            $this->service->page(
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }
    #[Get(
        path: '/admin/shop/shop/bindList',
        operationId: 'shop:shop:bindList',
        summary: '店铺绑定列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['店铺列表'],
    )]
//    #[ApiName(name: '店铺列表列表')]
//    #[RequestMapping(path: 'shop/shop/bindList', methods:"get")]
    #[Permission(code: 'shop:shop:bindList')]
    public function bindList(): Result
    {
        return $this->success(
            $this->service->bindList(
                $this->currentUser,
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }


    #[Post(
        path: '/admin/shop/shop',
        operationId: 'shop:shop:create',
        summary: '新增店铺列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['店铺列表'],
    )]
    #[ResultResponse(instance: new Result())]
//    #[ApiName(name: '新增店铺列表')]
//    #[RequestMapping(path: 'shop/shop', methods:"post")]
    #[Permission(code: 'shop:shop:create')]
    public function create(Request $request): Result
    {
        $this->service->create(array_merge($request->validated(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
        path: '/admin/shop/shop/{id}',
        operationId: 'shop:shop:update',
        summary: '保存店铺列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['店铺列表'],
    )]
    #[ResultResponse(instance: new Result())]
//    #[ApiName(name: '保存店铺列表')]
//    #[RequestMapping(path: 'shop/shop/{id}', methods:"put")]
    #[Permission(code: 'shop:shop:update')]
    public function save(int $id, Request $request): Result
    {
        $this->service->updateById($id, array_merge($request->validated(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }


    #[Delete(
        path: '/admin/shop/shop',
        operationId: 'shop:shop:delete',
        summary: '删除店铺列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['店铺列表'],
    )]
    #[ResultResponse(instance: new Result())]
//    #[ApiName(name: '删除店铺列表')]
//    #[RequestMapping(path: 'shop/shop', methods:"delete")]
    #[Permission(code: 'shop:shop:delete')]
    public function delete(): Result
    {
        $this->service->deleteById($this->getRequestData());
        return $this->success();
    }

}
